import {
  TabInfo,
  TabSession,
  TabStateSnapshot,
  WorkspaceTabMapping,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';

/**
 * 标签页会话管理器
 * 负责管理标签页与工作区的映射关系，以及标签页状态的持久化
 */
export class TabSessionManager {
  private static readonly MAX_HISTORY_ENTRIES = 10;
  private static readonly MAX_NAVIGATION_HISTORY = 5;

  /**
   * 生成唯一的会话ID
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建标签页会话
   */
  static async createTabSession(
    workspaceId: string,
    tabs: TabInfo[]
  ): Promise<OperationResult<TabSession>> {
    try {
      const sessionId = this.generateSessionId();
      const now = Date.now();

      // 为每个标签页添加会话信息
      const enhancedTabs = tabs.map(tab => ({
        ...tab,
        workspaceId,
        sessionId,
        lastActiveTime: tab.isActive ? now : (tab.lastActiveTime || now),
        navigationHistory: tab.navigationHistory || [tab.url]
      }));

      const session: TabSession = {
        id: sessionId,
        workspaceId,
        tabs: enhancedTabs,
        createdAt: now,
        lastUpdated: now,
        isActive: true
      };

      // 保存会话到存储
      const saveResult = await this.saveTabSession(session);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`创建标签页会话: ${sessionId}, 工作区: ${workspaceId}, 标签页数量: ${tabs.length}`);
      return { success: true, data: session };
    } catch (error) {
      console.error('创建标签页会话失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create tab session',
          details: error,
        },
      };
    }
  }

  /**
   * 保存标签页会话
   */
  static async saveTabSession(session: TabSession): Promise<OperationResult<void>> {
    try {
      const sessionsResult = await this.getAllTabSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }

      const sessions = sessionsResult.data!;
      
      // 查找现有会话并更新，或添加新会话
      const existingIndex = sessions.findIndex(s => s.id === session.id);
      if (existingIndex >= 0) {
        sessions[existingIndex] = { ...session, lastUpdated: Date.now() };
      } else {
        sessions.push(session);
      }

      // 限制会话数量，保留最近的会话
      if (sessions.length > this.MAX_HISTORY_ENTRIES) {
        sessions.sort((a, b) => b.lastUpdated - a.lastUpdated);
        sessions.splice(this.MAX_HISTORY_ENTRIES);
      }

      // 保存到存储
      await StorageManager.saveTabSessions(sessions);
      return { success: true };
    } catch (error) {
      console.error('保存标签页会话失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab session',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有标签页会话
   */
  static async getAllTabSessions(): Promise<OperationResult<TabSession[]>> {
    try {
      const result = await StorageManager.getTabSessions();
      return result;
    } catch (error) {
      console.error('获取标签页会话失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get tab sessions',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的最新标签页会话
   */
  static async getWorkspaceLatestSession(workspaceId: string): Promise<OperationResult<TabSession | null>> {
    try {
      const sessionsResult = await this.getAllTabSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }

      const sessions = sessionsResult.data!;
      const workspaceSessions = sessions
        .filter(s => s.workspaceId === workspaceId)
        .sort((a, b) => b.lastUpdated - a.lastUpdated);

      return {
        success: true,
        data: workspaceSessions.length > 0 ? workspaceSessions[0] : null
      };
    } catch (error) {
      console.error('获取工作区最新会话失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace latest session',
          details: error,
        },
      };
    }
  }

  /**
   * 创建标签页状态快照
   */
  static async createTabStateSnapshot(
    workspaceId: string,
    windowId: number,
    tabs: TabInfo[]
  ): Promise<OperationResult<TabStateSnapshot>> {
    try {
      const sessionId = this.generateSessionId();
      const snapshot: TabStateSnapshot = {
        workspaceId,
        windowId,
        tabs: tabs.map(tab => ({
          ...tab,
          workspaceId,
          sessionId,
          lastActiveTime: tab.isActive ? Date.now() : (tab.lastActiveTime || Date.now())
        })),
        timestamp: Date.now(),
        sessionId
      };

      // 保存快照
      const saveResult = await this.saveTabStateSnapshot(snapshot);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`创建标签页状态快照: ${sessionId}, 工作区: ${workspaceId}, 标签页数量: ${tabs.length}`);
      return { success: true, data: snapshot };
    } catch (error) {
      console.error('创建标签页状态快照失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create tab state snapshot',
          details: error,
        },
      };
    }
  }

  /**
   * 保存标签页状态快照
   */
  static async saveTabStateSnapshot(snapshot: TabStateSnapshot): Promise<OperationResult<void>> {
    try {
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (!snapshotsResult.success) {
        return { success: false, error: snapshotsResult.error };
      }

      const snapshots = snapshotsResult.data!;
      snapshots.push(snapshot);

      // 限制快照数量，保留最近的快照
      if (snapshots.length > this.MAX_HISTORY_ENTRIES) {
        snapshots.sort((a, b) => b.timestamp - a.timestamp);
        snapshots.splice(this.MAX_HISTORY_ENTRIES);
      }

      await StorageManager.saveTabStateSnapshots(snapshots);
      return { success: true };
    } catch (error) {
      console.error('保存标签页状态快照失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab state snapshot',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的最新状态快照
   */
  static async getWorkspaceLatestSnapshot(workspaceId: string): Promise<OperationResult<TabStateSnapshot | null>> {
    try {
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (!snapshotsResult.success) {
        return { success: false, error: snapshotsResult.error };
      }

      const snapshots = snapshotsResult.data!;
      const workspaceSnapshots = snapshots
        .filter(s => s.workspaceId === workspaceId)
        .sort((a, b) => b.timestamp - a.timestamp);

      return {
        success: true,
        data: workspaceSnapshots.length > 0 ? workspaceSnapshots[0] : null
      };
    } catch (error) {
      console.error('获取工作区最新快照失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace latest snapshot',
          details: error,
        },
      };
    }
  }

  /**
   * 更新标签页的导航历史
   */
  static updateTabNavigationHistory(tab: TabInfo, newUrl: string): TabInfo {
    const history = tab.navigationHistory || [];
    
    // 避免重复添加相同的URL
    if (history[history.length - 1] !== newUrl) {
      history.push(newUrl);
      
      // 限制历史记录长度
      if (history.length > this.MAX_NAVIGATION_HISTORY) {
        history.shift();
      }
    }

    return {
      ...tab,
      url: newUrl,
      navigationHistory: history,
      lastActiveTime: Date.now()
    };
  }

  /**
   * 清理过期的会话和快照
   */
  static async cleanupExpiredData(maxAgeMs: number = 7 * 24 * 60 * 60 * 1000): Promise<OperationResult<void>> {
    try {
      const now = Date.now();
      const cutoffTime = now - maxAgeMs;

      // 清理过期会话
      const sessionsResult = await this.getAllTabSessions();
      if (sessionsResult.success) {
        const validSessions = sessionsResult.data!.filter(s => s.lastUpdated > cutoffTime);
        await StorageManager.saveTabSessions(validSessions);
      }

      // 清理过期快照
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (snapshotsResult.success) {
        const validSnapshots = snapshotsResult.data!.filter(s => s.timestamp > cutoffTime);
        await StorageManager.saveTabStateSnapshots(validSnapshots);
      }

      console.log('清理过期数据完成');
      return { success: true };
    } catch (error) {
      console.error('清理过期数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to cleanup expired data',
          details: error,
        },
      };
    }
  }
}
