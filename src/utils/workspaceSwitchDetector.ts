import {
  WorkSpace,
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { TabSessionManager } from './tabSessionManager';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换事件类型
 */
export interface WorkspaceSwitchEvent {
  type: 'workspace-switch-detected' | 'workspace-switch-initiated' | 'workspace-switch-completed';
  fromWorkspaceId: string | null;
  toWorkspaceId: string;
  timestamp: number;
  tabsToSave?: TabInfo[];
  tabsToRestore?: TabInfo[];
}

/**
 * 工作区切换检测器
 * 负责检测和管理工作区切换事件
 */
export class WorkspaceSwitchDetector {
  private static currentWorkspaceId: string | null = null;
  private static switchInProgress: boolean = false;
  private static lastSwitchTime: number = 0;
  private static readonly SWITCH_DEBOUNCE_MS = 1000; // 防抖时间

  /**
   * 初始化检测器
   */
  static async initialize(): Promise<OperationResult<void>> {
    try {
      // 获取当前活跃工作区
      const currentResult = await StorageManager.getActiveWorkspaceId();
      if (currentResult.success) {
        this.currentWorkspaceId = currentResult.data;
      }

      console.log('工作区切换检测器初始化完成，当前工作区:', this.currentWorkspaceId);
      return { success: true };
    } catch (error) {
      console.error('工作区切换检测器初始化失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to initialize workspace switch detector',
          details: error,
        },
      };
    }
  }

  /**
   * 检测工作区切换事件
   */
  static async detectWorkspaceSwitch(newWorkspaceId: string): Promise<OperationResult<WorkspaceSwitchEvent | null>> {
    try {
      const now = Date.now();
      
      // 防抖处理，避免频繁切换
      if (this.switchInProgress || (now - this.lastSwitchTime) < this.SWITCH_DEBOUNCE_MS) {
        console.log('工作区切换正在进行中或过于频繁，跳过检测');
        return { success: true, data: null };
      }

      // 检查是否真的发生了切换
      if (this.currentWorkspaceId === newWorkspaceId) {
        console.log('工作区未发生变化，跳过切换检测');
        return { success: true, data: null };
      }

      console.log(`检测到工作区切换: ${this.currentWorkspaceId} -> ${newWorkspaceId}`);

      // 创建切换事件
      const switchEvent: WorkspaceSwitchEvent = {
        type: 'workspace-switch-detected',
        fromWorkspaceId: this.currentWorkspaceId,
        toWorkspaceId: newWorkspaceId,
        timestamp: now
      };

      // 如果有当前工作区，获取需要保存的标签页
      if (this.currentWorkspaceId) {
        const tabsResult = await this.getWorkspaceTabsToSave(this.currentWorkspaceId);
        if (tabsResult.success) {
          switchEvent.tabsToSave = tabsResult.data;
        }
      }

      // 获取需要恢复的标签页
      const restoreResult = await this.getWorkspaceTabsToRestore(newWorkspaceId);
      if (restoreResult.success) {
        switchEvent.tabsToRestore = restoreResult.data;
      }

      return { success: true, data: switchEvent };
    } catch (error) {
      console.error('检测工作区切换失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect workspace switch',
          details: error,
        },
      };
    }
  }

  /**
   * 开始工作区切换流程
   */
  static async startWorkspaceSwitch(switchEvent: WorkspaceSwitchEvent): Promise<OperationResult<void>> {
    try {
      console.log('开始工作区切换流程:', switchEvent);
      
      this.switchInProgress = true;
      this.lastSwitchTime = Date.now();

      // 触发切换开始事件
      const startEvent: WorkspaceSwitchEvent = {
        ...switchEvent,
        type: 'workspace-switch-initiated',
        timestamp: Date.now()
      };

      // 通知其他组件切换开始
      await this.notifyWorkspaceSwitchEvent(startEvent);

      return { success: true };
    } catch (error) {
      console.error('开始工作区切换失败:', error);
      this.switchInProgress = false;
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to start workspace switch',
          details: error,
        },
      };
    }
  }

  /**
   * 完成工作区切换流程
   */
  static async completeWorkspaceSwitch(
    switchEvent: WorkspaceSwitchEvent,
    success: boolean = true
  ): Promise<OperationResult<void>> {
    try {
      console.log('完成工作区切换流程:', switchEvent, '成功:', success);

      if (success) {
        // 更新当前工作区ID
        this.currentWorkspaceId = switchEvent.toWorkspaceId;

        // 触发切换完成事件
        const completeEvent: WorkspaceSwitchEvent = {
          ...switchEvent,
          type: 'workspace-switch-completed',
          timestamp: Date.now()
        };

        // 通知其他组件切换完成
        await this.notifyWorkspaceSwitchEvent(completeEvent);
      }

      this.switchInProgress = false;
      return { success: true };
    } catch (error) {
      console.error('完成工作区切换失败:', error);
      this.switchInProgress = false;
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to complete workspace switch',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区需要保存的标签页
   */
  private static async getWorkspaceTabsToSave(workspaceId: string): Promise<OperationResult<TabInfo[]>> {
    try {
      // 获取工作区信息
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前窗口中与工作区相关的标签页
      const relatedTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!relatedTabsResult.success) {
        return { success: false, error: relatedTabsResult.error };
      }

      const relatedTabs = relatedTabsResult.data!;

      // 为标签页添加元数据
      const enhancedTabs = relatedTabs.map(tab => 
        TabManager.enhanceTabWithWorkspaceMetadata(tab, workspaceId)
      );

      console.log(`工作区 ${workspaceId} 需要保存 ${enhancedTabs.length} 个标签页`);
      return { success: true, data: enhancedTabs };
    } catch (error) {
      console.error('获取工作区需要保存的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace tabs to save',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区需要恢复的标签页
   */
  private static async getWorkspaceTabsToRestore(workspaceId: string): Promise<OperationResult<TabInfo[]>> {
    try {
      // 从最新的会话中获取标签页
      const sessionResult = await TabSessionManager.getWorkspaceLatestSession(workspaceId);
      if (!sessionResult.success) {
        return { success: false, error: sessionResult.error };
      }

      const session = sessionResult.data;
      if (!session) {
        console.log(`工作区 ${workspaceId} 没有保存的会话`);
        return { success: true, data: [] };
      }

      console.log(`工作区 ${workspaceId} 需要恢复 ${session.tabs.length} 个标签页`);
      return { success: true, data: session.tabs };
    } catch (error) {
      console.error('获取工作区需要恢复的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace tabs to restore',
          details: error,
        },
      };
    }
  }

  /**
   * 通知工作区切换事件
   */
  private static async notifyWorkspaceSwitchEvent(event: WorkspaceSwitchEvent): Promise<void> {
    try {
      // 这里可以添加事件通知逻辑，比如发送消息给侧边栏
      console.log('工作区切换事件通知:', event);
      
      // 可以通过 chrome.runtime.sendMessage 发送消息给其他组件
      // 或者通过存储变化来通知
    } catch (error) {
      console.error('通知工作区切换事件失败:', error);
    }
  }

  /**
   * 获取当前工作区ID
   */
  static getCurrentWorkspaceId(): string | null {
    return this.currentWorkspaceId;
  }

  /**
   * 检查是否正在切换中
   */
  static isSwitchInProgress(): boolean {
    return this.switchInProgress;
  }

  /**
   * 强制重置切换状态（用于错误恢复）
   */
  static resetSwitchState(): void {
    this.switchInProgress = false;
    console.log('工作区切换状态已重置');
  }
}
