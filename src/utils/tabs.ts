import {
  TabInfo,
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 标签页管理类
 */
export class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        isSuspended: tab.discarded || false, // 包含挂起状态
      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab(): Promise<OperationResult<TabInfo>> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active tab found',
          },
        };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 检查URL是否已在标签页中打开
   */
  static async findTabByUrl(url: string): Promise<OperationResult<TabInfo | null>> {
    try {
      // 首先尝试精确匹配
      let tabs = await chrome.tabs.query({ url });

      // 如果精确匹配失败，尝试域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const allTabs = await chrome.tabs.query({});
          tabs = allTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          // URL解析失败，返回空结果
          return { success: true, data: null };
        }
      }

      if (tabs.length === 0) {
        return { success: true, data: null };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to find tab by URL',
          details: error,
        },
      };
    }
  }

  /**
   * 创建新标签页
   */
  static async createTab(url: string, pinned: boolean = false, active: boolean = true): Promise<OperationResult<TabInfo>> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active,
      });

      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab',
          details: error,
        },
      };
    }
  }

  /**
   * 激活标签页
   */
  static async activateTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to activate tab',
          details: error,
        },
      };
    }
  }

  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId: number, pinned: boolean): Promise<OperationResult<void>> {
    try {
      console.log(`${pinned ? 'Pinning' : 'Unpinning'} tab ${tabId}`);
      await chrome.tabs.update(tabId, { pinned });
      console.log(`Successfully ${pinned ? 'pinned' : 'unpinned'} tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to ${pinned ? 'pin' : 'unpin'} tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to pin/unpin tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭标签页
   */
  static async closeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区相关的标签页
   */
  static async getWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;

      const allTabs = allTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);
      
      const relatedTabs = allTabs.filter(tab => 
        workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: relatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取非工作区相关的标签页
   */
  static async getNonWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;

      const allTabs = allTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);
      
      const nonRelatedTabs = allTabs.filter(tab => 
        !workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get non-workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为用户手动打开的
   */
  static async isUserOpenedTab(tabId: number): Promise<boolean> {
    try {
      // 这里可以实现更复杂的逻辑来判断标签页是否为用户手动打开
      // 目前简单地检查标签页是否为固定状态
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }

  /**
   * 挂起标签页（释放内存）
   */
  static async suspendTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`挂起标签页: ${tabId}`);
      await chrome.tabs.discard(tabId);
      console.log(`成功挂起标签页: ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`挂起标签页失败: ${tabId}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to suspend tab',
          details: error,
        },
      };
    }
  }

  /**
   * 批量挂起标签页
   */
  static async suspendTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      console.log(`批量挂起 ${tabIds.length} 个标签页`);
      const results = await Promise.allSettled(
        tabIds.map(tabId => chrome.tabs.discard(tabId))
      );

      const failedCount = results.filter(result => result.status === 'rejected').length;
      if (failedCount > 0) {
        console.warn(`${failedCount} 个标签页挂起失败`);
      }

      console.log(`成功挂起 ${tabIds.length - failedCount} 个标签页`);
      return { success: true };
    } catch (error) {
      console.error('批量挂起标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to suspend tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否已被挂起
   */
  static async isTabSuspended(tabId: number): Promise<OperationResult<boolean>> {
    try {
      const tab = await chrome.tabs.get(tabId);
      // 挂起的标签页通常具有 discarded 属性为 true
      return {
        success: true,
        data: tab.discarded || false
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check tab suspension status',
          details: error,
        },
      };
    }
  }

  /**
   * 恢复挂起的标签页
   */
  static async resumeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`恢复标签页: ${tabId}`);
      // 激活标签页会自动恢复挂起状态
      await chrome.tabs.update(tabId, { active: true });
      console.log(`成功恢复标签页: ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`恢复标签页失败: ${tabId}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to resume tab',
          details: error,
        },
      };
    }
  }
}
