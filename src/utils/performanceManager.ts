import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime: number;
  duration: number;
  success: boolean;
  tabCount?: number;
  errorMessage?: string;
}

/**
 * 批处理配置接口
 */
export interface BatchConfig {
  batchSize: number;
  delayBetweenBatches: number;
  maxConcurrent: number;
  timeoutPerBatch: number;
}

/**
 * 性能管理器
 * 负责性能监控、批处理优化和错误处理
 */
export class PerformanceManager {
  private static metrics: PerformanceMetrics[] = [];
  private static readonly MAX_METRICS_HISTORY = 100;
  private static readonly PERFORMANCE_THRESHOLD_MS = 2000; // 2秒性能阈值

  // 默认批处理配置
  private static readonly DEFAULT_BATCH_CONFIG: BatchConfig = {
    batchSize: 10,
    delayBetweenBatches: 100,
    maxConcurrent: 3,
    timeoutPerBatch: 5000
  };

  /**
   * 开始性能监控
   */
  static startPerformanceMonitoring(operationName: string): string {
    const monitoringId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 存储开始时间
    const startTime = performance.now();
    (globalThis as any)[`perf_${monitoringId}`] = {
      operationName,
      startTime,
      tabCount: 0
    };

    console.log(`开始性能监控: ${operationName} (ID: ${monitoringId})`);
    return monitoringId;
  }

  /**
   * 结束性能监控
   */
  static endPerformanceMonitoring(
    monitoringId: string, 
    success: boolean = true, 
    tabCount?: number,
    errorMessage?: string
  ): PerformanceMetrics | null {
    try {
      const perfData = (globalThis as any)[`perf_${monitoringId}`];
      if (!perfData) {
        console.warn(`性能监控数据未找到: ${monitoringId}`);
        return null;
      }

      const endTime = performance.now();
      const duration = endTime - perfData.startTime;

      const metrics: PerformanceMetrics = {
        operationName: perfData.operationName,
        startTime: perfData.startTime,
        endTime,
        duration,
        success,
        tabCount: tabCount || perfData.tabCount,
        errorMessage
      };

      // 记录性能指标
      this.recordMetrics(metrics);

      // 检查性能阈值
      if (duration > this.PERFORMANCE_THRESHOLD_MS) {
        console.warn(`性能警告: ${perfData.operationName} 耗时 ${duration.toFixed(2)}ms，超过阈值 ${this.PERFORMANCE_THRESHOLD_MS}ms`);
      }

      // 清理临时数据
      delete (globalThis as any)[`perf_${monitoringId}`];

      console.log(`性能监控结束: ${perfData.operationName}, 耗时: ${duration.toFixed(2)}ms, 成功: ${success}`);
      return metrics;
    } catch (error) {
      console.error('结束性能监控失败:', error);
      return null;
    }
  }

  /**
   * 记录性能指标
   */
  private static recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // 限制历史记录数量
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics.shift();
    }
  }

  /**
   * 获取性能统计
   */
  static getPerformanceStats(): {
    totalOperations: number;
    averageDuration: number;
    successRate: number;
    slowOperations: PerformanceMetrics[];
  } {
    if (this.metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        slowOperations: []
      };
    }

    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const successCount = this.metrics.filter(m => m.success).length;
    const slowOperations = this.metrics.filter(m => m.duration > this.PERFORMANCE_THRESHOLD_MS);

    return {
      totalOperations: this.metrics.length,
      averageDuration: totalDuration / this.metrics.length,
      successRate: (successCount / this.metrics.length) * 100,
      slowOperations
    };
  }

  /**
   * 批处理标签页操作
   */
  static async batchProcessTabs<T>(
    items: T[],
    processor: (item: T) => Promise<any>,
    config: Partial<BatchConfig> = {}
  ): Promise<OperationResult<any[]>> {
    const finalConfig = { ...this.DEFAULT_BATCH_CONFIG, ...config };
    const monitoringId = this.startPerformanceMonitoring(`batch_process_${items.length}_items`);

    try {
      console.log(`开始批处理 ${items.length} 个项目，批大小: ${finalConfig.batchSize}`);

      const results: any[] = [];
      const errors: any[] = [];

      // 分批处理
      for (let i = 0; i < items.length; i += finalConfig.batchSize) {
        const batch = items.slice(i, i + finalConfig.batchSize);
        console.log(`处理批次 ${Math.floor(i / finalConfig.batchSize) + 1}/${Math.ceil(items.length / finalConfig.batchSize)}`);

        try {
          // 并发处理当前批次，但限制并发数
          const batchPromises = batch.map(item => 
            this.withTimeout(processor(item), finalConfig.timeoutPerBatch)
          );

          const batchResults = await Promise.allSettled(batchPromises);
          
          // 处理批次结果
          batchResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              results.push(result.value);
            } else {
              errors.push({
                item: batch[index],
                error: result.reason
              });
              console.error(`批处理项目失败:`, result.reason);
            }
          });

          // 批次间延迟
          if (i + finalConfig.batchSize < items.length) {
            await this.delay(finalConfig.delayBetweenBatches);
          }
        } catch (error) {
          console.error(`批次处理失败:`, error);
          errors.push({ batch, error });
        }
      }

      this.endPerformanceMonitoring(monitoringId, errors.length === 0, items.length);

      if (errors.length > 0) {
        console.warn(`批处理完成，但有 ${errors.length} 个错误`);
      }

      return {
        success: true,
        data: results
      };
    } catch (error) {
      this.endPerformanceMonitoring(monitoringId, false, items.length, error.message);
      console.error('批处理失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Batch processing failed',
          details: error,
        },
      };
    }
  }

  /**
   * 带超时的Promise包装
   */
  private static withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) => 
        setTimeout(() => reject(new Error(`Operation timeout after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  /**
   * 延迟函数
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 重试机制
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000,
    backoffMultiplier: number = 2
  ): Promise<OperationResult<T>> {
    let lastError: any;
    let currentDelay = delayMs;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`尝试操作，第 ${attempt}/${maxRetries} 次`);
        const result = await operation();
        
        if (attempt > 1) {
          console.log(`操作在第 ${attempt} 次尝试后成功`);
        }
        
        return { success: true, data: result };
      } catch (error) {
        lastError = error;
        console.warn(`操作失败，第 ${attempt}/${maxRetries} 次尝试:`, error);

        if (attempt < maxRetries) {
          console.log(`等待 ${currentDelay}ms 后重试...`);
          await this.delay(currentDelay);
          currentDelay *= backoffMultiplier;
        }
      }
    }

    console.error(`操作在 ${maxRetries} 次尝试后仍然失败:`, lastError);
    return {
      success: false,
      error: {
        code: ERROR_CODES.TAB_ERROR,
        message: `Operation failed after ${maxRetries} retries`,
        details: lastError,
      },
    };
  }

  /**
   * 内存使用监控
   */
  static getMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
  } {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
      };
    }
    
    return {
      used: 0,
      total: 0,
      percentage: 0
    };
  }

  /**
   * 清理性能数据
   */
  static clearMetrics(): void {
    this.metrics = [];
    console.log('性能指标已清理');
  }

  /**
   * 导出性能报告
   */
  static exportPerformanceReport(): string {
    const stats = this.getPerformanceStats();
    const memoryUsage = this.getMemoryUsage();
    
    const report = {
      timestamp: new Date().toISOString(),
      performanceStats: stats,
      memoryUsage,
      recentMetrics: this.metrics.slice(-10) // 最近10条记录
    };

    return JSON.stringify(report, null, 2);
  }
}
