import {
  WorkSpace,
  TabSession,
  TabStateSnapshot,
  WorkspaceTabMapping,
  OperationResult,
  ExportData
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabSessionManager } from './tabSessionManager';
import { WindowManager } from './windowManager';
import { ERROR_CODES } from './constants';

/**
 * 备份数据接口
 */
export interface BackupData {
  version: string;
  timestamp: number;
  workspaces: WorkSpace[];
  tabSessions: TabSession[];
  tabStateSnapshots: TabStateSnapshot[];
  workspaceTabMappings: WorkspaceTabMapping[];
  activeWorkspaceId: string | null;
  lastActiveWorkspaceIds: string[];
  windowMappings: { [workspaceId: string]: number };
}

/**
 * 数据备份和恢复管理器
 * 负责系统重启或崩溃后的数据恢复
 */
export class DataBackupManager {
  private static readonly BACKUP_VERSION = '1.0.0';
  private static readonly BACKUP_KEY = 'workspace_backup_data';
  private static readonly AUTO_BACKUP_INTERVAL = 5 * 60 * 1000; // 5分钟
  private static readonly MAX_BACKUP_HISTORY = 10;

  private static autoBackupTimer: NodeJS.Timeout | null = null;

  /**
   * 启动自动备份
   */
  static startAutoBackup(): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
    }

    this.autoBackupTimer = setInterval(async () => {
      try {
        await this.createBackup();
        console.log('自动备份完成');
      } catch (error) {
        console.error('自动备份失败:', error);
      }
    }, this.AUTO_BACKUP_INTERVAL);

    console.log('自动备份已启动，间隔:', this.AUTO_BACKUP_INTERVAL / 1000, '秒');
  }

  /**
   * 停止自动备份
   */
  static stopAutoBackup(): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
      this.autoBackupTimer = null;
      console.log('自动备份已停止');
    }
  }

  /**
   * 创建完整备份
   */
  static async createBackup(): Promise<OperationResult<BackupData>> {
    try {
      console.log('开始创建数据备份...');

      // 获取所有数据
      const [
        workspacesResult,
        tabSessionsResult,
        snapshotsResult,
        mappingsResult,
        activeIdResult,
        lastActiveIdsResult
      ] = await Promise.all([
        StorageManager.getWorkspaces(),
        StorageManager.getTabSessions(),
        StorageManager.getTabStateSnapshots(),
        StorageManager.getWorkspaceTabMappings(),
        StorageManager.getActiveWorkspaceId(),
        StorageManager.getLastActiveWorkspaceIds()
      ]);

      // 检查所有操作是否成功
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const backupData: BackupData = {
        version: this.BACKUP_VERSION,
        timestamp: Date.now(),
        workspaces: workspacesResult.data!,
        tabSessions: tabSessionsResult.success ? tabSessionsResult.data! : [],
        tabStateSnapshots: snapshotsResult.success ? snapshotsResult.data! : [],
        workspaceTabMappings: mappingsResult.success ? mappingsResult.data! : [],
        activeWorkspaceId: activeIdResult.success ? activeIdResult.data : null,
        lastActiveWorkspaceIds: lastActiveIdsResult.success ? lastActiveIdsResult.data! : [],
        windowMappings: this.getWindowMappings()
      };

      // 保存备份
      const saveResult = await this.saveBackup(backupData);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log('数据备份创建完成');
      return { success: true, data: backupData };
    } catch (error) {
      console.error('创建数据备份失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create backup',
          details: error,
        },
      };
    }
  }

  /**
   * 保存备份数据
   */
  private static async saveBackup(backupData: BackupData): Promise<OperationResult<void>> {
    try {
      // 获取现有备份历史
      const existingBackups = await this.getBackupHistory();
      
      // 添加新备份
      existingBackups.unshift(backupData);
      
      // 限制备份数量
      if (existingBackups.length > this.MAX_BACKUP_HISTORY) {
        existingBackups.splice(this.MAX_BACKUP_HISTORY);
      }

      // 保存到存储
      await chrome.storage.local.set({
        [this.BACKUP_KEY]: existingBackups
      });

      return { success: true };
    } catch (error) {
      console.error('保存备份数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save backup',
          details: error,
        },
      };
    }
  }

  /**
   * 获取备份历史
   */
  static async getBackupHistory(): Promise<BackupData[]> {
    try {
      const result = await chrome.storage.local.get(this.BACKUP_KEY);
      return result[this.BACKUP_KEY] || [];
    } catch (error) {
      console.error('获取备份历史失败:', error);
      return [];
    }
  }

  /**
   * 获取最新备份
   */
  static async getLatestBackup(): Promise<OperationResult<BackupData | null>> {
    try {
      const backups = await this.getBackupHistory();
      const latestBackup = backups.length > 0 ? backups[0] : null;
      
      return { success: true, data: latestBackup };
    } catch (error) {
      console.error('获取最新备份失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get latest backup',
          details: error,
        },
      };
    }
  }

  /**
   * 从备份恢复数据
   */
  static async restoreFromBackup(backupData?: BackupData): Promise<OperationResult<void>> {
    try {
      console.log('开始从备份恢复数据...');

      let dataToRestore = backupData;
      
      // 如果没有提供备份数据，使用最新的备份
      if (!dataToRestore) {
        const latestResult = await this.getLatestBackup();
        if (!latestResult.success || !latestResult.data) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.STORAGE_ERROR,
              message: 'No backup data available for restore',
            },
          };
        }
        dataToRestore = latestResult.data;
      }

      // 恢复基础数据
      await Promise.all([
        StorageManager.saveWorkspaces(dataToRestore.workspaces),
        StorageManager.saveTabSessions(dataToRestore.tabSessions),
        StorageManager.saveTabStateSnapshots(dataToRestore.tabStateSnapshots),
        StorageManager.saveWorkspaceTabMappings(dataToRestore.workspaceTabMappings),
        StorageManager.setActiveWorkspaceId(dataToRestore.activeWorkspaceId),
        StorageManager.setLastActiveWorkspaceIds(dataToRestore.lastActiveWorkspaceIds)
      ]);

      // 恢复窗口映射（如果需要）
      this.restoreWindowMappings(dataToRestore.windowMappings);

      console.log('数据恢复完成');
      return { success: true };
    } catch (error) {
      console.error('从备份恢复数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to restore from backup',
          details: error,
        },
      };
    }
  }

  /**
   * 检测系统重启或崩溃后的恢复需求
   */
  static async detectAndRecover(): Promise<OperationResult<boolean>> {
    try {
      console.log('检测是否需要数据恢复...');

      // 检查是否有未正常关闭的标记
      const result = await chrome.storage.local.get('extension_running');
      const wasRunning = result.extension_running || false;

      if (wasRunning) {
        console.log('检测到异常关闭，开始自动恢复...');
        
        // 从最新备份恢复
        const restoreResult = await this.restoreFromBackup();
        if (restoreResult.success) {
          console.log('自动恢复完成');
        } else {
          console.error('自动恢复失败:', restoreResult.error);
        }

        // 清除运行标记
        await chrome.storage.local.remove('extension_running');
        
        return { success: true, data: true }; // 表示进行了恢复
      } else {
        console.log('正常启动，无需恢复');
        return { success: true, data: false }; // 表示无需恢复
      }
    } catch (error) {
      console.error('检测和恢复失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to detect and recover',
          details: error,
        },
      };
    }
  }

  /**
   * 标记扩展正在运行
   */
  static async markExtensionRunning(): Promise<void> {
    try {
      await chrome.storage.local.set({ extension_running: true });
    } catch (error) {
      console.error('标记扩展运行状态失败:', error);
    }
  }

  /**
   * 标记扩展正常关闭
   */
  static async markExtensionStopped(): Promise<void> {
    try {
      await chrome.storage.local.remove('extension_running');
    } catch (error) {
      console.error('清除扩展运行状态失败:', error);
    }
  }

  /**
   * 获取窗口映射
   */
  private static getWindowMappings(): { [workspaceId: string]: number } {
    const mappings: { [workspaceId: string]: number } = {};
    // 这里可以从WindowManager获取当前的窗口映射
    // 由于WindowManager的映射是私有的，这里暂时返回空对象
    return mappings;
  }

  /**
   * 恢复窗口映射
   */
  private static restoreWindowMappings(mappings: { [workspaceId: string]: number }): void {
    // 这里可以恢复窗口映射关系
    // 由于窗口ID在重启后会变化，这个功能可能需要特殊处理
    console.log('窗口映射恢复（暂未实现）:', mappings);
  }

  /**
   * 导出备份数据为JSON
   */
  static async exportBackupAsJson(): Promise<OperationResult<string>> {
    try {
      const backupResult = await this.createBackup();
      if (!backupResult.success) {
        return { success: false, error: backupResult.error };
      }

      const exportData: ExportData = {
        version: this.BACKUP_VERSION,
        exportedAt: Date.now(),
        workspaces: backupResult.data!.workspaces,
        settings: (await StorageManager.getSettings()).data!,
        tabSessions: backupResult.data!.tabSessions
      };

      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      console.error('导出备份数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to export backup as JSON',
          details: error,
        },
      };
    }
  }
}
