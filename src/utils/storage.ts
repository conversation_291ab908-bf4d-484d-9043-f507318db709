import {
  WorkSpace,
  Settings,
  StorageData,
  OperationResult
} from '@/types/workspace';
import { 
  STORAGE_KEYS, 
  DEFAULT_SETTINGS, 
  ERROR_CODES 
} from './constants';

/**
 * 存储管理类
 */
export class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData(): Promise<OperationResult<StorageData>> {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
      ]);

      const data: StorageData = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],
      };

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get storage data',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有工作区
   */
  static async getWorkspaces(): Promise<OperationResult<WorkSpace[]>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      
      // 按order字段排序
      workspaces.sort((a: WorkSpace, b: WorkSpace) => a.order - b.order);
      
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取单个工作区
   */
  static async getWorkspace(id: string): Promise<OperationResult<WorkSpace>> {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }

    const workspace = result.data!.find(w => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`,
        },
      };
    }

    return { success: true, data: workspace };
  }

  /**
   * 获取设置
   */
  static async getSettings(): Promise<OperationResult<Settings>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get settings',
          details: error,
        },
      };
    }
  }

  /**
   * 保存设置
   */
  static async saveSettings(settings: Partial<Settings>): Promise<OperationResult<void>> {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }

      const updatedSettings = { ...currentResult.data!, ...settings };
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save settings',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId(): Promise<OperationResult<string | null>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id: string | null): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id,
      });
      
      // 更新最近使用的工作区列表
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds: string[] = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      
      // 移除已存在的ID
      lastActiveIds = lastActiveIds.filter(id => id !== workspaceId);
      
      // 添加到开头
      lastActiveIds.unshift(workspaceId);
      
      // 限制数量
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data!.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update last active workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 清除所有数据
   */
  static async clearAll(): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear storage',
          details: error,
        },
      };
    }
  }

  /**
   * 监听存储变化
   */
  static onChanged(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local') {
        callback(changes);
      }
    });
  }

  /**
   * 导出数据
   */
  static async exportData(): Promise<OperationResult<string>> {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }

      const exportData = {
        version: '1.0.0',
        exportedAt: Date.now(),
        workspaces: dataResult.data!.workspaces,
        settings: dataResult.data!.settings,
      };

      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to export data',
          details: error,
        },
      };
    }
  }

  /**
   * 导入数据
   */
  static async importData(jsonData: string): Promise<OperationResult<void>> {
    try {
      const importData = JSON.parse(jsonData);

      if (importData.workspaces) {
        await this.saveWorkspaces(importData.workspaces);
      }

      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }

      if (importData.tabSessions) {
        await this.saveTabSessions(importData.tabSessions);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to import data',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页会话列表
   */
  static async getTabSessions(): Promise<OperationResult<import('@/types/workspace').TabSession[]>> {
    try {
      const result = await chrome.storage.local.get('tabSessions');
      const sessions = result.tabSessions || [];
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get tab sessions',
          details: error,
        },
      };
    }
  }

  /**
   * 保存标签页会话列表
   */
  static async saveTabSessions(sessions: import('@/types/workspace').TabSession[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({ tabSessions: sessions });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab sessions',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页状态快照列表
   */
  static async getTabStateSnapshots(): Promise<OperationResult<import('@/types/workspace').TabStateSnapshot[]>> {
    try {
      const result = await chrome.storage.local.get('tabStateSnapshots');
      const snapshots = result.tabStateSnapshots || [];
      return { success: true, data: snapshots };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get tab state snapshots',
          details: error,
        },
      };
    }
  }

  /**
   * 保存标签页状态快照列表
   */
  static async saveTabStateSnapshots(snapshots: import('@/types/workspace').TabStateSnapshot[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({ tabStateSnapshots: snapshots });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab state snapshots',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区标签页映射列表
   */
  static async getWorkspaceTabMappings(): Promise<OperationResult<import('@/types/workspace').WorkspaceTabMapping[]>> {
    try {
      const result = await chrome.storage.local.get('workspaceTabMappings');
      const mappings = result.workspaceTabMappings || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace tab mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区标签页映射列表
   */
  static async saveWorkspaceTabMappings(mappings: import('@/types/workspace').WorkspaceTabMapping[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({ workspaceTabMappings: mappings });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save workspace tab mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 获取最近活跃的工作区ID列表
   */
  static async getLastActiveWorkspaceIds(): Promise<OperationResult<string[]>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      const ids = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      return { success: true, data: ids };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get last active workspace IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 设置最近活跃的工作区ID列表
   */
  static async setLastActiveWorkspaceIds(ids: string[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: ids,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set last active workspace IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 清理所有存储数据（用于重置）
   */
  static async clearAllData(): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.clear();
      console.log('所有存储数据已清理');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear all data',
          details: error,
        },
      };
    }
  }

  /**
   * 获取存储使用情况
   */
  static async getStorageUsage(): Promise<OperationResult<{ used: number; quota: number }>> {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;

      return {
        success: true,
        data: {
          used: usage,
          quota: quota
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get storage usage',
          details: error,
        },
      };
    }
  }
}
