import { OperationResult, WorkSpace, TabInfo } from '@/types/workspace';
import { StorageManager } from './storage';
import { DataBackupManager } from './dataBackupManager';
import { WindowManager } from './windowManager';
import { TabSessionManager } from './tabSessionManager';
import { WorkspaceSwitchDetector } from './workspaceSwitchDetector';
import { ERROR_CODES } from './constants';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  STORAGE_ERROR = 'STORAGE_ERROR',
  TAB_ERROR = 'TAB_ERROR',
  WINDOW_ERROR = 'WINDOW_ERROR',
  WORKSPACE_SWITCH_ERROR = 'WORKSPACE_SWITCH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误记录接口
 */
export interface ErrorRecord {
  id: string;
  type: ErrorType;
  message: string;
  details: any;
  timestamp: number;
  context: string;
  resolved: boolean;
  recoveryAttempts: number;
}

/**
 * 恢复策略接口
 */
export interface RecoveryStrategy {
  name: string;
  description: string;
  execute: () => Promise<OperationResult<void>>;
  canRecover: (error: ErrorRecord) => boolean;
}

/**
 * 错误处理和恢复管理器
 */
export class ErrorRecoveryManager {
  private static errorHistory: ErrorRecord[] = [];
  private static readonly MAX_ERROR_HISTORY = 50;
  private static readonly MAX_RECOVERY_ATTEMPTS = 3;
  private static recoveryStrategies: RecoveryStrategy[] = [];

  /**
   * 初始化错误恢复管理器
   */
  static initialize(): void {
    this.setupRecoveryStrategies();
    this.setupGlobalErrorHandlers();
    console.log('错误恢复管理器已初始化');
  }

  /**
   * 设置恢复策略
   */
  private static setupRecoveryStrategies(): void {
    this.recoveryStrategies = [
      {
        name: 'storage_recovery',
        description: '存储错误恢复',
        canRecover: (error) => error.type === ErrorType.STORAGE_ERROR,
        execute: async () => {
          console.log('执行存储错误恢复...');
          // 尝试从备份恢复
          const backupResult = await DataBackupManager.getLatestBackup();
          if (backupResult.success && backupResult.data) {
            return await DataBackupManager.restoreFromBackup(backupResult.data);
          }
          return { success: false, error: { code: ERROR_CODES.STORAGE_ERROR, message: 'No backup available' } };
        }
      },
      {
        name: 'window_recovery',
        description: '窗口错误恢复',
        canRecover: (error) => error.type === ErrorType.WINDOW_ERROR,
        execute: async () => {
          console.log('执行窗口错误恢复...');
          // 清理无效窗口并重新创建
          await WindowManager.cleanupInvalidWindows();
          await WindowManager.ensureWindowsHidden();
          return { success: true };
        }
      },
      {
        name: 'workspace_switch_recovery',
        description: '工作区切换错误恢复',
        canRecover: (error) => error.type === ErrorType.WORKSPACE_SWITCH_ERROR,
        execute: async () => {
          console.log('执行工作区切换错误恢复...');
          // 重置切换状态
          WorkspaceSwitchDetector.resetSwitchState();
          return { success: true };
        }
      },
      {
        name: 'tab_recovery',
        description: '标签页错误恢复',
        canRecover: (error) => error.type === ErrorType.TAB_ERROR,
        execute: async () => {
          console.log('执行标签页错误恢复...');
          // 尝试从会话恢复标签页
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success) {
            const activeWorkspace = workspacesResult.data!.find(w => w.isActive);
            if (activeWorkspace) {
              const sessionResult = await TabSessionManager.getWorkspaceLatestSession(activeWorkspace.id);
              if (sessionResult.success && sessionResult.data) {
                // 这里可以添加标签页恢复逻辑
                console.log('找到可恢复的标签页会话');
              }
            }
          }
          return { success: true };
        }
      }
    ];
  }

  /**
   * 设置全局错误处理器
   */
  private static setupGlobalErrorHandlers(): void {
    // 监听未捕获的错误
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.handleError(
          ErrorType.UNKNOWN_ERROR,
          event.error?.message || 'Unknown error',
          event.error,
          'global_error_handler'
        );
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(
          ErrorType.UNKNOWN_ERROR,
          event.reason?.message || 'Unhandled promise rejection',
          event.reason,
          'unhandled_rejection'
        );
      });
    }
  }

  /**
   * 处理错误
   */
  static async handleError(
    type: ErrorType,
    message: string,
    details: any,
    context: string
  ): Promise<string> {
    const errorId = this.generateErrorId();
    
    const errorRecord: ErrorRecord = {
      id: errorId,
      type,
      message,
      details,
      timestamp: Date.now(),
      context,
      resolved: false,
      recoveryAttempts: 0
    };

    // 记录错误
    this.recordError(errorRecord);
    
    console.error(`错误处理 [${errorId}]:`, {
      type,
      message,
      context,
      details
    });

    // 尝试自动恢复
    await this.attemptRecovery(errorRecord);

    return errorId;
  }

  /**
   * 记录错误
   */
  private static recordError(error: ErrorRecord): void {
    this.errorHistory.push(error);
    
    // 限制错误历史数量
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift();
    }
  }

  /**
   * 尝试自动恢复
   */
  private static async attemptRecovery(error: ErrorRecord): Promise<void> {
    if (error.recoveryAttempts >= this.MAX_RECOVERY_ATTEMPTS) {
      console.warn(`错误 ${error.id} 已达到最大恢复尝试次数`);
      return;
    }

    // 查找适用的恢复策略
    const applicableStrategies = this.recoveryStrategies.filter(strategy => 
      strategy.canRecover(error)
    );

    if (applicableStrategies.length === 0) {
      console.log(`错误 ${error.id} 没有适用的恢复策略`);
      return;
    }

    error.recoveryAttempts++;
    
    for (const strategy of applicableStrategies) {
      try {
        console.log(`尝试恢复策略: ${strategy.name} (${strategy.description})`);
        
        const result = await strategy.execute();
        
        if (result.success) {
          error.resolved = true;
          console.log(`错误 ${error.id} 已通过策略 ${strategy.name} 恢复`);
          return;
        } else {
          console.warn(`恢复策略 ${strategy.name} 失败:`, result.error);
        }
      } catch (recoveryError) {
        console.error(`恢复策略 ${strategy.name} 执行失败:`, recoveryError);
      }
    }
  }

  /**
   * 手动恢复选项
   */
  static async getManualRecoveryOptions(): Promise<{
    canRestoreFromBackup: boolean;
    canResetWorkspaces: boolean;
    canClearAllData: boolean;
    backupCount: number;
    lastBackupTime: number | null;
  }> {
    try {
      const backups = await DataBackupManager.getBackupHistory();
      const latestBackup = backups.length > 0 ? backups[0] : null;

      return {
        canRestoreFromBackup: backups.length > 0,
        canResetWorkspaces: true,
        canClearAllData: true,
        backupCount: backups.length,
        lastBackupTime: latestBackup ? latestBackup.timestamp : null
      };
    } catch (error) {
      console.error('获取手动恢复选项失败:', error);
      return {
        canRestoreFromBackup: false,
        canResetWorkspaces: true,
        canClearAllData: true,
        backupCount: 0,
        lastBackupTime: null
      };
    }
  }

  /**
   * 执行手动恢复
   */
  static async executeManualRecovery(option: 'restore_backup' | 'reset_workspaces' | 'clear_all'): Promise<OperationResult<void>> {
    try {
      console.log(`执行手动恢复: ${option}`);

      switch (option) {
        case 'restore_backup':
          const backupResult = await DataBackupManager.getLatestBackup();
          if (backupResult.success && backupResult.data) {
            return await DataBackupManager.restoreFromBackup(backupResult.data);
          } else {
            return {
              success: false,
              error: {
                code: ERROR_CODES.STORAGE_ERROR,
                message: 'No backup available for restore'
              }
            };
          }

        case 'reset_workspaces':
          // 清除工作区数据但保留设置
          await StorageManager.saveWorkspaces([]);
          await StorageManager.setActiveWorkspaceId(null);
          await StorageManager.setLastActiveWorkspaceIds([]);
          return { success: true };

        case 'clear_all':
          // 清除所有数据
          return await StorageManager.clearAllData();

        default:
          return {
            success: false,
            error: {
              code: ERROR_CODES.UNKNOWN_ERROR,
              message: 'Unknown recovery option'
            }
          };
      }
    } catch (error) {
      console.error('执行手动恢复失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.UNKNOWN_ERROR,
          message: 'Manual recovery failed',
          details: error
        }
      };
    }
  }

  /**
   * 获取错误统计
   */
  static getErrorStats(): {
    totalErrors: number;
    resolvedErrors: number;
    errorsByType: { [key in ErrorType]?: number };
    recentErrors: ErrorRecord[];
  } {
    const errorsByType: { [key in ErrorType]?: number } = {};
    
    this.errorHistory.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
    });

    return {
      totalErrors: this.errorHistory.length,
      resolvedErrors: this.errorHistory.filter(e => e.resolved).length,
      errorsByType,
      recentErrors: this.errorHistory.slice(-10)
    };
  }

  /**
   * 生成错误ID
   */
  private static generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理错误历史
   */
  static clearErrorHistory(): void {
    this.errorHistory = [];
    console.log('错误历史已清理');
  }

  /**
   * 导出错误报告
   */
  static exportErrorReport(): string {
    const stats = this.getErrorStats();
    
    const report = {
      timestamp: new Date().toISOString(),
      errorStats: stats,
      errorHistory: this.errorHistory,
      recoveryStrategies: this.recoveryStrategies.map(s => ({
        name: s.name,
        description: s.description
      }))
    };

    return JSON.stringify(report, null, 2);
  }
}
