import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { TabSessionManager } from './tabSessionManager';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前活跃工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true,
      };

      // 1. 如果有当前工作区，保存其标签页状态并移动到专用窗口
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await this.saveAndMoveCurrentWorkspaceTabs(currentWorkspace);
      }

      // 2. 从目标工作区恢复标签页状态
      await this.restoreWorkspaceTabs(workspace);

      // 3. 打开工作区中尚未打开的网站
      await this.openWorkspaceWebsites(workspace);

      // 4. 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 5. 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      // 6. 如果需要，聚焦到第一个标签页
      if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
        const firstWebsite = workspace.websites[0];
        const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
        if (tabResult.success && tabResult.data) {
          await TabManager.activateTab(tabResult.data.id);
        }
      }

      console.log(`成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 保存当前工作区的标签页状态并移动到专用窗口
   */
  private static async saveAndMoveCurrentWorkspaceTabs(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`保存并移动工作区 ${workspace.name} 的标签页`);

      // 获取当前窗口中与工作区相关的标签页
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaceTabs = workspaceTabsResult.data!;
      if (workspaceTabs.length === 0) {
        console.log(`工作区 ${workspace.name} 没有相关标签页需要保存`);
        return { success: true };
      }

      // 为标签页添加元数据
      const enhancedTabs = workspaceTabs.map(tab =>
        TabManager.enhanceTabWithWorkspaceMetadata(tab, workspace.id)
      );

      // 创建标签页会话
      const sessionResult = await TabSessionManager.createTabSession(workspace.id, enhancedTabs);
      if (sessionResult.success) {
        console.log(`成功创建工作区 ${workspace.name} 的标签页会话`);
      } else {
        console.error(`创建标签页会话失败:`, sessionResult.error);
      }

      // 创建状态快照
      const currentWindow = await chrome.windows.getCurrent();
      const snapshotResult = await TabSessionManager.createTabStateSnapshot(
        workspace.id,
        currentWindow.id!,
        enhancedTabs
      );
      if (snapshotResult.success) {
        console.log(`成功创建工作区 ${workspace.name} 的状态快照`);
      }

      // 移动标签页到专用窗口
      const tabIds = workspaceTabs.map(tab => tab.id);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );

      if (moveResult.success) {
        console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);
      } else {
        console.error(`移动标签页到专用窗口失败:`, moveResult.error);
      }

      return { success: true }; // 即使部分操作失败也不阻断流程
    } catch (error) {
      console.error(`保存并移动当前工作区标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to save and move current workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 恢复工作区的标签页状态
   */
  private static async restoreWorkspaceTabs(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`恢复工作区 ${workspace.name} 的标签页状态`);

      // 1. 首先尝试从专用窗口移动现有标签页
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);
      let restoredFromWindow = 0;

      if (moveResult.success) {
        restoredFromWindow = moveResult.data!.length;
        console.log(`从专用窗口恢复了 ${restoredFromWindow} 个标签页`);
      }

      // 2. 如果专用窗口中没有标签页，尝试从会话中恢复
      if (restoredFromWindow === 0) {
        const sessionResult = await TabSessionManager.getWorkspaceLatestSession(workspace.id);
        if (sessionResult.success && sessionResult.data) {
          const session = sessionResult.data;
          console.log(`从会话中恢复工作区 ${workspace.name} 的 ${session.tabs.length} 个标签页`);

          await this.restoreTabsFromSession(session.tabs);
        } else {
          console.log(`工作区 ${workspace.name} 没有可恢复的会话数据`);
        }
      }

      // 3. 恢复标签页的原始状态（固定状态、激活状态等）
      await this.restoreTabStates(workspace);

      return { success: true };
    } catch (error) {
      console.error(`恢复工作区标签页状态失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to restore workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 从会话数据中恢复标签页
   */
  private static async restoreTabsFromSession(sessionTabs: TabInfo[]): Promise<OperationResult<void>> {
    try {
      console.log(`从会话数据中恢复 ${sessionTabs.length} 个标签页`);

      for (const tabInfo of sessionTabs) {
        try {
          // 检查标签页是否已经存在
          const existingTabResult = await TabManager.findTabByUrl(tabInfo.url);

          if (!existingTabResult.success || !existingTabResult.data) {
            // 创建新标签页
            const createResult = await TabManager.createTab(
              tabInfo.url,
              tabInfo.isPinned,
              false // 不立即激活
            );

            if (createResult.success) {
              console.log(`成功恢复标签页: ${tabInfo.title}`);

              // 如果原来是挂起状态，挂起新创建的标签页
              if (tabInfo.isSuspended && createResult.data) {
                await TabManager.suspendTab(createResult.data.id);
              }
            } else {
              console.error(`恢复标签页失败 ${tabInfo.title}:`, createResult.error);
            }
          } else {
            console.log(`标签页已存在，跳过恢复: ${tabInfo.title}`);
          }
        } catch (error) {
          console.error(`恢复单个标签页失败 ${tabInfo.title}:`, error);
          // 继续处理其他标签页
        }
      }

      return { success: true };
    } catch (error) {
      console.error(`从会话数据中恢复标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to restore tabs from session',
          details: error,
        },
      };
    }
  }

  /**
   * 恢复标签页的状态（固定、激活等）
   */
  private static async restoreTabStates(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`恢复工作区 ${workspace.name} 的标签页状态`);

      // 获取当前窗口中与工作区相关的标签页
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        return { success: true }; // 不阻断流程
      }

      const workspaceTabs = workspaceTabsResult.data!;

      // 恢复固定状态
      for (const tab of workspaceTabs) {
        try {
          // 查找对应的网站配置
          const website = workspace.websites.find(w => tab.url.startsWith(w.url));
          if (website && website.isPinned !== tab.isPinned) {
            await TabManager.pinTab(tab.id, website.isPinned);
            console.log(`恢复标签页固定状态: ${tab.title} -> ${website.isPinned}`);
          }
        } catch (error) {
          console.error(`恢复标签页状态失败 ${tab.title}:`, error);
        }
      }

      return { success: true };
    } catch (error) {
      console.error(`恢复标签页状态失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to restore tab states',
          details: error,
        },
      };
    }
  }

  /**
   * 打开工作区中尚未打开的网站
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`检查并打开工作区 ${workspace.name} 中缺失的网站`);

      for (const website of workspace.websites) {
        try {
          // 检查是否已经打开
          const existingTabResult = await TabManager.findTabByUrl(website.url);

          if (!existingTabResult.success || !existingTabResult.data) {
            // 创建新标签页（不固定，因为我们使用专用窗口架构）
            console.log(`创建新标签页: ${website.title}`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`成功创建标签页: ${website.title}`);
            } else {
              console.error(`创建标签页失败 ${website.title}:`, newTabResult.error);
            }
          } else {
            console.log(`标签页已存在: ${website.title}`);
          }
        } catch (error) {
          console.error(`处理网站 ${website.title} 时出错:`, error);
          // 继续处理其他网站，不要因为一个失败而停止
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }



  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 查找包含当前活跃标签页URL的工作区
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => 
          activeTab.url.startsWith(website.url)
        )
      );

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }
}
