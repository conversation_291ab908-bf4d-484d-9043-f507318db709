import { StorageManager } from '../utils/storage';
import { WorkspaceSwitcher } from '../utils/workspaceSwitcher';
import { WorkspaceSwitchDetector } from '../utils/workspaceSwitchDetector';
import { TabSessionManager } from '../utils/tabSessionManager';
import { WindowManager } from '../utils/windowManager';
import { DataBackupManager } from '../utils/dataBackupManager';
import { COMMANDS } from '../utils/constants';

/**
 * Chrome扩展后台脚本
 */
class BackgroundService {
  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    // 检测并恢复数据（如果需要）
    await this.initializeDataRecovery();

    // 设置侧边栏行为
    await this.setupSidePanel();

    // 初始化工作区切换检测器
    await this.initializeWorkspaceSwitchDetector();

    // 监听命令
    this.setupCommandListeners();

    // 监听标签页事件
    this.setupTabListeners();

    // 监听窗口事件
    this.setupWindowListeners();

    // 监听存储变化
    this.setupStorageListeners();

    // 初始化默认数据
    await this.initializeDefaultData();

    // 设置数据备份和定期清理
    this.setupDataBackupAndCleanup();

    // 标记扩展正在运行
    await DataBackupManager.markExtensionRunning();

    console.log('WorkSpace Pro background service initialized');
  }

  /**
   * 设置侧边栏
   */
  private async setupSidePanel(): Promise<void> {
    try {
      // 设置侧边栏在点击扩展图标时打开
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error('Failed to setup side panel:', error);
    }
  }

  /**
   * 初始化数据恢复
   */
  private async initializeDataRecovery(): Promise<void> {
    try {
      console.log('开始数据恢复检测...');
      const recoveryResult = await DataBackupManager.detectAndRecover();

      if (recoveryResult.success) {
        if (recoveryResult.data) {
          console.log('检测到异常关闭，已自动恢复数据');
          this.showNotification('数据已自动恢复', '🔄');
        } else {
          console.log('正常启动，无需数据恢复');
        }
      } else {
        console.error('数据恢复检测失败:', recoveryResult.error);
      }
    } catch (error) {
      console.error('初始化数据恢复时出错:', error);
    }
  }

  /**
   * 初始化工作区切换检测器
   */
  private async initializeWorkspaceSwitchDetector(): Promise<void> {
    try {
      const result = await WorkspaceSwitchDetector.initialize();
      if (result.success) {
        console.log('工作区切换检测器初始化成功');
      } else {
        console.error('工作区切换检测器初始化失败:', result.error);
      }
    } catch (error) {
      console.error('初始化工作区切换检测器时出错:', error);
    }
  }

  /**
   * 设置数据备份和定期清理
   */
  private setupDataBackupAndCleanup(): void {
    // 启动自动备份
    DataBackupManager.startAutoBackup();

    // 每小时清理一次过期数据
    setInterval(async () => {
      try {
        await TabSessionManager.cleanupExpiredData();
        console.log('定期清理过期数据完成');
      } catch (error) {
        console.error('定期清理过期数据失败:', error);
      }
    }, 60 * 60 * 1000); // 1小时

    // 每10分钟检查和维护窗口状态
    setInterval(async () => {
      try {
        await WindowManager.cleanupInvalidWindows();
        await WindowManager.ensureWindowsHidden();
        console.log('定期窗口维护完成');
      } catch (error) {
        console.error('定期窗口维护失败:', error);
      }
    }, 10 * 60 * 1000); // 10分钟

    // 监听扩展卸载事件，进行清理
    chrome.runtime.onSuspend.addListener(async () => {
      try {
        console.log('扩展即将挂起，执行清理操作...');
        await DataBackupManager.createBackup();
        await DataBackupManager.markExtensionStopped();
        DataBackupManager.stopAutoBackup();
        console.log('扩展清理完成');
      } catch (error) {
        console.error('扩展清理失败:', error);
      }
    });
  }

  /**
   * 设置命令监听器
   */
  private setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          default:
            console.log('Unknown command:', command);
        }
      } catch (error) {
        console.error('Error handling command:', command, error);
      }
    });
  }

  /**
   * 设置标签页监听器
   */
  private setupTabListeners(): void {
    // 监听标签页激活
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        // 检测当前应该激活的工作区
        const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (detectedResult.success && detectedResult.data) {
          const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();

          // 如果检测到的工作区与当前工作区不同，触发切换检测
          if (currentWorkspaceId !== detectedResult.data.id) {
            await this.handleWorkspaceSwitch(detectedResult.data.id);
          }
        }
      } catch (error) {
        console.error('Error handling tab activation:', error);
      }
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        try {
          // 检测是否需要触发工作区切换
          const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
          if (detectedResult.success && detectedResult.data) {
            const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();

            if (currentWorkspaceId !== detectedResult.data.id) {
              await this.handleWorkspaceSwitch(detectedResult.data.id);
            }
          }

          console.log('Tab updated:', tab.url);
        } catch (error) {
          console.error('Error handling tab update:', error);
        }
      }
    });

    // 监听标签页创建
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log('Tab created:', tab.url);

        // 新标签页可能需要关联到当前工作区
        const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();
        if (currentWorkspaceId && tab.id) {
          // 这里可以添加逻辑将新标签页关联到当前工作区
          console.log(`新标签页 ${tab.id} 可能需要关联到工作区 ${currentWorkspaceId}`);
        }
      } catch (error) {
        console.error('Error handling tab creation:', error);
      }
    });

    // 监听标签页关闭
    chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
      try {
        console.log('Tab removed:', tabId);
        // 这里可以添加清理逻辑
      } catch (error) {
        console.error('Error handling tab removal:', error);
      }
    });
  }

  /**
   * 设置窗口事件监听器
   */
  private setupWindowListeners(): void {
    // 监听窗口焦点变化
    chrome.windows.onFocusChanged.addListener(async (windowId) => {
      try {
        if (windowId === chrome.windows.WINDOW_ID_NONE) {
          return; // 没有窗口获得焦点
        }

        // 检查是否是工作区专用窗口获得了焦点
        const workspaceId = WindowManager.getWindowWorkspaceId(windowId);
        if (workspaceId) {
          console.log(`工作区专用窗口 ${windowId} 获得焦点，重新隐藏`);
          // 立即重新隐藏窗口
          await WindowManager.hideWorkspaceWindow(windowId);
        }
      } catch (error) {
        console.error('处理窗口焦点变化失败:', error);
      }
    });

    // 监听窗口创建
    chrome.windows.onCreated.addListener(async (window) => {
      try {
        console.log('新窗口创建:', window.id);
        // 这里可以添加新窗口的处理逻辑
      } catch (error) {
        console.error('处理窗口创建失败:', error);
      }
    });

    // 监听窗口移除
    chrome.windows.onRemoved.addListener(async (windowId) => {
      try {
        console.log('窗口移除:', windowId);

        // 检查是否是工作区专用窗口被移除
        const workspaceId = WindowManager.getWindowWorkspaceId(windowId);
        if (workspaceId) {
          console.log(`工作区专用窗口 ${windowId} 被移除，清理映射关系`);
          // 清理映射关系会在WindowManager的cleanupInvalidWindows中处理
        }
      } catch (error) {
        console.error('处理窗口移除失败:', error);
      }
    });
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      console.log('Storage changed:', changes);

      // 通知侧边栏更新
      this.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('No workspaces found, creating default workspace templates');
        
        // 可以选择性地创建一些默认工作区
        // 这里暂时不自动创建，让用户自己选择
      }
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }

  /**
   * 处理工作区切换
   */
  private async handleWorkspaceSwitch(newWorkspaceId: string): Promise<void> {
    try {
      // 检查是否正在切换中
      if (WorkspaceSwitchDetector.isSwitchInProgress()) {
        console.log('工作区切换正在进行中，跳过新的切换请求');
        return;
      }

      // 检测工作区切换事件
      const detectResult = await WorkspaceSwitchDetector.detectWorkspaceSwitch(newWorkspaceId);
      if (!detectResult.success || !detectResult.data) {
        return;
      }

      const switchEvent = detectResult.data;
      console.log('检测到工作区切换事件:', switchEvent);

      // 开始切换流程
      const startResult = await WorkspaceSwitchDetector.startWorkspaceSwitch(switchEvent);
      if (!startResult.success) {
        console.error('开始工作区切换失败:', startResult.error);
        return;
      }

      // 执行实际的工作区切换
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(newWorkspaceId, {
        closeOtherTabs: false,
        focusFirstTab: false,
      });

      // 完成切换流程
      await WorkspaceSwitchDetector.completeWorkspaceSwitch(switchEvent, switchResult.success);

      if (switchResult.success) {
        // 获取工作区信息用于通知
        const workspaceResult = await StorageManager.getWorkspace(newWorkspaceId);
        if (workspaceResult.success) {
          const workspace = workspaceResult.data!;
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        }
      } else {
        console.error('工作区切换失败:', switchResult.error);
        this.showNotification('工作区切换失败', '❌');
      }
    } catch (error) {
      console.error('处理工作区切换时出错:', error);
      // 重置切换状态
      WorkspaceSwitchDetector.resetSwitchState();
    }
  }

  /**
   * 根据索引切换工作区
   */
  private async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('Failed to get workspaces:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        await this.handleWorkspaceSwitch(workspace.id);
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error('Error switching workspace by index:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private async toggleSidePanel(): Promise<void> {
    try {
      // 获取当前活跃的标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id!;
        // 这里可以实现侧边栏的切换逻辑
        // 由于Chrome API限制，我们主要依赖用户点击扩展图标
        console.log('Toggle side panel for tab:', tabId);
      }
    } catch (error) {
      console.error('Error toggling side panel:', error);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, icon?: string): void {
    try {
      // 创建简单的通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'WorkSpace Pro',
        message: `${icon || '🚀'} ${message}`,
      });
      console.log('Notification shown:', message);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private notifySidePanelUpdate(_changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 这里可以通过消息传递通知侧边栏更新
      // 由于侧边栏是独立的页面，我们主要依赖存储监听
      console.log('Notifying side panel of storage changes');
    } catch (error) {
      console.error('Error notifying side panel:', error);
    }
  }
}

// 初始化后台服务
new BackgroundService();
