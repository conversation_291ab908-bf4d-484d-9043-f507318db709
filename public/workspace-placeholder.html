<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkSpace Pro - 工作区专用窗口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .workspace-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: inline-block;
            padding: 1rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .workspace-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .workspace-description {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: rgba(51, 65, 85, 0.6);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #60a5fa;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .info-section {
            background: rgba(51, 65, 85, 0.4);
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .info-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #f1f5f9;
        }

        .info-list {
            list-style: none;
            text-align: left;
        }

        .info-list li {
            padding: 0.5rem 0;
            color: #cbd5e1;
            display: flex;
            align-items: center;
        }

        .info-list li::before {
            content: "✨";
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .footer {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(148, 163, 184, 0.1);
            color: #64748b;
            font-size: 0.9rem;
        }

        .brand {
            font-weight: 600;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .container {
            animation: fadeIn 0.6s ease-out;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        /* 标签页列表样式 */
        .tabs-section {
            background: rgba(51, 65, 85, 0.4);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
            max-height: 400px;
            overflow-y: auto;
        }

        .tabs-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        }

        .tabs-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #f1f5f9;
        }

        .tabs-search {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 0.5rem 0.75rem;
            background: rgba(51, 65, 85, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        .search-input::placeholder {
            color: #94a3b8;
        }

        .tabs-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .tabs-stats {
            margin-bottom: 1rem;
            padding: 0.5rem 0.75rem;
            background: rgba(51, 65, 85, 0.3);
            border-radius: 6px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stats-text {
            font-size: 0.85rem;
            color: #94a3b8;
        }

        .empty-tabs {
            text-align: center;
            padding: 2rem;
            color: #94a3b8;
            font-size: 0.9rem;
            background: rgba(30, 41, 59, 0.3);
            border-radius: 8px;
            border: 1px dashed rgba(148, 163, 184, 0.2);
        }

        .error-message {
            text-align: center;
            padding: 2rem;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .error-message h3 {
            color: #ef4444;
            margin-bottom: 1rem;
        }

        .error-message p {
            color: #fca5a5;
            margin: 0;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .btn-secondary {
            background: rgba(71, 85, 105, 0.8);
            color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: rgba(71, 85, 105, 1);
        }

        .btn-success {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
        }

        .tab-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 8px;
            border: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.2s;
        }

        .tab-item:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(148, 163, 184, 0.2);
        }

        .tab-item.selected {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .tab-checkbox {
            margin-right: 0.75rem;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .search-highlight {
            background: rgba(251, 191, 36, 0.3);
            color: #fbbf24;
            padding: 0.1rem 0.2rem;
            border-radius: 3px;
        }

        .tab-actions {
            display: flex;
            gap: 0.25rem;
            margin-left: 0.5rem;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .btn-small:hover {
            transform: translateY(-1px);
        }

        .btn-small:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .tab-favicon {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
            border-radius: 2px;
        }

        .tab-info {
            flex: 1;
            min-width: 0;
        }

        .tab-title {
            font-size: 0.9rem;
            color: #f1f5f9;
            margin-bottom: 0.2rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-url {
            font-size: 0.75rem;
            color: #94a3b8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 0.5rem;
        }

        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-suspended {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .status-pinned {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .empty-tabs {
            text-align: center;
            padding: 2rem;
            color: #94a3b8;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(30, 41, 59, 0.95);
            border-radius: 12px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            border: 1px solid rgba(148, 163, 184, 0.2);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 1rem;
        }

        .modal-message {
            color: #cbd5e1;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .modal-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="workspace-icon" id="workspaceIcon">
            🚀
        </div>
        
        <h1 class="workspace-name" id="workspaceName">
            工作区专用窗口
        </h1>
        
        <p class="workspace-description">
            此窗口专门用于存储和管理工作区的标签页。<br>
            当您切换到其他工作区时，相关标签页会自动移动到这里保存。
        </p>





        <!-- 标签页列表区域 -->
        <div class="tabs-section" id="tabsSection">
            <div class="tabs-header">
                <h3 class="tabs-title">📋 存储的标签页</h3>
                <div class="tabs-search">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索标签页标题或URL...">
                    <button class="btn btn-secondary" id="clearSearchBtn">清除</button>
                </div>
                <div class="tabs-actions">
                    <button class="btn btn-secondary" id="selectAllBtn">全选</button>
                    <button class="btn btn-primary" id="suspendSelectedBtn" disabled>挂起选中</button>
                    <button class="btn btn-success" id="restoreSelectedBtn" disabled>恢复选中</button>
                    <button class="btn btn-danger" id="deleteSelectedBtn" disabled>删除选中</button>
                </div>
            </div>
            <div class="tabs-stats" id="tabsStats">
                <span class="stats-text">共 <span id="totalTabsCount">0</span> 个标签页，显示 <span id="filteredTabsCount">0</span> 个</span>
            </div>
            <div id="tabsList" class="tabs-list">
                <!-- 标签页列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 确认删除模态框 -->
        <div class="modal" id="deleteModal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">⚠️ 确认删除</h3>
                <p class="modal-message" id="deleteMessage">
                    确定要删除选中的标签页吗？此操作无法撤销。
                </p>
                <div class="modal-actions">
                    <button class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                    <button class="btn btn-danger" id="confirmDeleteBtn">删除</button>
                </div>
            </div>
        </div>

        <div class="footer">
            由 <span class="brand">WorkSpace Pro</span> 提供 • 专业的标签页工作区管理
        </div>
    </div>

    <!-- 引用外部JavaScript文件 -->
    <script src="workspace-placeholder.js"></script>
</body>
</html>
